<?php

use App\Http\Controllers\Admin\EmailController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DonorController;
use App\Http\Controllers\HomePageController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\SubscribersForLatestEvents;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;




//home page route
Route::get('/', [HomePageController::class, 'home'])->name('home');
Route::get('/home-data', [HomePageController::class, 'homePageData'])->name('homePageData');
Route::get('/leaderboard', [HomePageController::class, 'leaderBoard'])->name('leaderBoard');
Route::get('/leaderboard-data', [HomePageController::class, 'leaderBoardData'])->name('leaderBoardData');
Route::get('/gauntlet', [HomePageController::class, 'gauntlet'])->name('gauntlet');
Route::get('/contact', [HomePageController::class, 'contact'])->name('contact');
Route::get('/about', [HomePageController::class, 'about'])->name('about');
Route::get('/donate', [HomePageController::class, 'donate'])->name('donate');
Route::get('search-athlete/{search}', [HomePageController::class, 'athletes'])->name('search-athelete-for-donation');
Route::post('/send-message', [HomePageController::class, 'storeMessage'])->name('storeMessage');


//subscriber for latest event routes

Route::post('/subscribe', [SubscribersForLatestEvents::class, 'subscribeForLatestEvents'])->name('subscribeForLatestEvents');
Route::post('/unsubscribe', [SubscribersForLatestEvents::class, 'unSubscribeForLatestEvents'])->name('unSubscribeFromLatestEvents');


//Auth Routes

Route::get('/register', [AuthController::class, 'register'])->middleware('redirectIfAuthenticatedByRole')->name('register');
Route::post('/register', [AuthController::class, 'store'])->name('register.store');
Route::get('program-selection/{slug}', [AuthController::class, 'programSelectionPage'])->name('program-selection');
Route::get('api/program-selection/{slug}', [AuthController::class, 'programSelection'])->name('program-selection.api');
Route::post('selected-program', [AuthController::class, 'selectedProgram'])->name('selected-program');

Route::middleware(['preventBackHistory', 'redirectIfAuthenticatedByRole'])->group(function () {
    Route::get('/login', [AuthController::class, 'login'])->name('login');
});

Route::post('/login', [AuthController::class, 'loginUser'])->name('login.user');


Route::prefix('user')->name('user.')->middleware(['auth', 'isUser'])->group(function () {

    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');
    Route::post('/edit-profile', [UserController::class, 'editProfile'])->name('editProfile');
    Route::post('/update-password', [UserController::class, 'updatePassword'])->name('updatePassword');
});

//donation page route
Route::get('/user/{slug}', [UserController::class, 'donationPage'])->name('donationPage')->middleware('preventBackHistory');




// payment routes
Route::post('/details', [DonorController::class, 'detailView'])->name('donor.details');
Route::post('/create-payment-intent', [PaymentController::class, 'createPaymentIntent'])->name('createPaymentIntent');
Route::post('/store-transaction', [PaymentController::class, 'storeTransaction'])->name('storeTransaction');
Route::post('/create-payment-intent-for-org-one-time', [PaymentController::class, 'createPaymentIntentforOrgOneTime'])->name('createPaymentIntentforOrgOneTime');
Route::post('/store-transaction-for-org-one-time', [PaymentController::class, 'storeTransactionForOrgOneTime'])->name('storeTransactionForOrgOneTime');
Route::post('/create-payment-intent-for-org', [PaymentController::class, 'createPaymentIntentforOrgDonation'])->name('createPaymentIntentforOrgDonation');
Route::post('/create-subscription', [PaymentController::class, 'createSubscriptionForOrg'])->name('createSubscriptionForOrg');
Route::post('confirm-subscription', [PaymentController::class, 'confirmSubscription'])->name('confirmSubscription');
Route::post('store-transaction-org-donation', [PaymentController::class, 'storeTransactionForOrgDonation'])->name('storeTransactionForOrgDonation');
Route::post('/record-payment-failure', [PaymentController::class, 'handleClientSidePaymentFailure'])->name('recordPaymentFailure');
Route::get('/thank-you', [PaymentController::class, 'successPage'])->name('successPage');


//admin routes

Route::prefix('admin')->name('admin.')->middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/get-all-users', [AdminController::class, 'getAllUsers'])->name('getAllUsers');
    Route::get('donation-view', [AdminController::class, 'donationList'])->name('donationList');
    Route::get('/get-all-donors', [AdminController::class, 'getAllDonors'])->name('getAllDonors');
    Route::get('/mail-template', [AdminController::class, 'mailView'])->name('mailView');
    Route::post('edit/current-year-goal', [AdminController::class, 'editCurrentYearGoal'])->name('editCurrrentYearGoal');
    Route::post('/add-admin-account', [AdminController::class, 'addAdminAccount'])->name('addAdminAccount');
    Route::post('/add-user-account', [AdminController::class, 'addUserAccount'])->name('addUserAccount');
    Route::get('/users/{id}/edit', [AdminController::class, 'editUser']);
    Route::post('edit/user', [AdminController::class, 'storeEditedUser'])->name('storeEditedUser');
    Route::post('/delete/user', [AdminController::class, 'deleteUser'])->name('deleteUser');
    Route::get('/faqs', [AdminController::class, 'faqs'])->name('faqs');
    Route::post('/faq-store', [AdminController::class, 'faqStore'])->name('faqs.store');
    Route::put('/faq-update', [AdminController::class, 'faqUpdate'])->name('faqs.update');
    Route::delete('/faq-delete', [AdminController::class, 'faqDelete'])->name('faqs.delete');
    Route::get('/export-donors', [AdminController::class, 'exportDonors'])->name('exportDonors');
    Route::get('/subscriptions', [AdminController::class, 'subscriptions'])->name('subscriptions');

    // Failed Transactions routes
    Route::get('/failed-transactions', [App\Http\Controllers\Admin\FailedTransactionController::class, 'index'])->name('failed-transactions.index');
    Route::get('/failed-transactions/{id}', [App\Http\Controllers\Admin\FailedTransactionController::class, 'show'])->name('failed-transactions.show');
    Route::post('/failed-transactions/{id}/resolve', [App\Http\Controllers\Admin\FailedTransactionController::class, 'resolve'])->name('failed-transactions.resolve');
    Route::post('/failed-transactions/{id}/abandon', [App\Http\Controllers\Admin\FailedTransactionController::class, 'abandon'])->name('failed-transactions.abandon');
    Route::post('/failed-transactions/{id}/retry', [App\Http\Controllers\Admin\FailedTransactionController::class, 'retry'])->name('failed-transactions.retry');
    Route::get('/failed-transactions-api', [App\Http\Controllers\Admin\FailedTransactionController::class, 'getFailedTransactions'])->name('failed-transactions.api');
    Route::get('/failed-transactions-stats', [App\Http\Controllers\Admin\FailedTransactionController::class, 'getStatistics'])->name('failed-transactions.stats');
    Route::get('/failed-transactions-export', [App\Http\Controllers\Admin\FailedTransactionController::class, 'export'])->name('failed-transactions.export');
});



//admin message routes-->

Route::prefix('admin/messages')->name('admin.messages.')->middleware(['auth', 'isAdmin'])->group(function () {
    Route::get('/', [MessageController::class, 'index'])->name('index');
    Route::get('/filter', [MessageController::class, 'filter'])->name('filter');
    Route::get('/{id}', [MessageController::class, 'show'])->name('show');
    Route::post('/{message}/reply', [MessageController::class, 'reply'])->name('reply');
    Route::post('/{message}/read', [MessageController::class, 'markAsRead'])->name('markAsRead');
    Route::post('/mark-all-read', [MessageController::class, 'markAllAsRead'])->name('markAllAsRead');
});



// admin Email Builder Routes
Route::middleware(['auth', 'isAdmin'])->prefix('admin')->group(function () {
    Route::get('/send-mail', [EmailController::class, 'showEmailBuilder'])->name('admin.send-mail');
    Route::post('/send-mail', [EmailController::class, 'sendEmail'])->name('admin.send-mail');
    Route::post('/upload-image', [EmailController::class, 'uploadImage'])->name('admin.upload-image');
    Route::post('/save-template', [EmailController::class, 'saveTemplate'])->name('admin.save-template');
    Route::get('/get-templates', [EmailController::class, 'getTemplates'])->name('admin.get-templates');
    Route::get('/get-template/{id}', [EmailController::class, 'getTemplate'])->name('admin.get-template');
    Route::get('/email-templates', [EmailController::class, 'showTemplates'])->name('admin.email-templates');
});





//log out route
Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');










//testing routes
Route::post('/upload-image', function (Request $request) {
    if ($request->hasFile('file')) {
        $image = $request->file('file');
        $path = $image->store('uploads', 'public');

        return response()->json(['location' => asset('storage/' . $path)]);
    }

    return response()->json(['error' => 'No image uploaded'], 400);
});
